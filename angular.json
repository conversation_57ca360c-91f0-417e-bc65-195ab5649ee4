{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"mvp": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": {"base": "dist"}, "allowedCommonJsDependencies": ["object-path", "apexcharts", "prismjs"], "index": "src/index.html", "polyfills": ["@angular/localize/init", "src/polyfills.ts"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "src/styles.scss", "node_modules/ngx-spinner/animations/ball-clip-rotate.css", "node_modules/ngx-toastr/toastr.css", "node_modules/@ng-select/ng-select/themes/default.theme.css", "node_modules/angular-calendar/css/angular-calendar.css"], "scripts": [], "browser": "src/main.ts"}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "4kb", "maximumError": "6kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true}, "dev": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev.ts"}], "outputHashing": "all"}, "local": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.local.ts"}], "outputHashing": "all"}, "develop": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.develop.ts"}], "outputHashing": "all"}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "mvp:build:production"}, "development": {"buildTarget": "mvp:build:development"}, "dev": {"buildTarget": "mvp:build:dev"}, "local": {"buildTarget": "mvp:build:local"}, "develop": {"buildTarget": "mvp:build:develop"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "mvp:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": []}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}, "cli": {"analytics": false}, "schematics": {"@angular-eslint/schematics:application": {"setParserOptionsProject": true}, "@angular-eslint/schematics:library": {"setParserOptionsProject": true}}}