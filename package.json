{"name": "mvp", "version": "8.1.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "build:prod": "ng build --configuration production", "build:dev": "ng build --configuration dev", "build:develop": "ng build --configuration develop", "build:local": "ng build --configuration local", "watch": "ng build --watch --configuration development", "start:dev": "ng serve --configuration dev", "start:local": "ng serve --configuration local", "rtl": "webpack --config webpack-rtl.config.js", "test": "ng test", "lint": "ng lint", "lint:fix": "ng lint --fix", "amplify:checkout:local": "amplify checkout env amplifydev", "amplify:checkout:dev": "amplify checkout env dev", "amplify:checkout:prod": "amplify checkout env prod"}, "private": true, "dependencies": {"@amcharts/amcharts5": "^5.3.12", "@amcharts/amcharts5-geodata": "^5.1.1", "@angular/animations": "18.2.13", "@angular/cdk": "18.2.14", "@angular/common": "18.2.13", "@angular/compiler": "18.2.13", "@angular/core": "18.2.13", "@angular/forms": "18.2.13", "@angular/localize": "18.2.13", "@angular/material": "^18.2.14", "@angular/platform-browser": "18.2.13", "@angular/platform-browser-dynamic": "18.2.13", "@angular/router": "18.2.13", "@apollo/client": "^3.7.0", "@aws-sdk/client-sns": "^3.218.0", "@aws-sdk/client-transcribe": "^3.760.0", "@ckeditor/ckeditor5-angular": "^5.2.0", "@ckeditor/ckeditor5-build-classic": "^36.0.1", "@fortawesome/fontawesome-free": "6.1.1", "@ng-bootstrap/ng-bootstrap": "^17.0.1", "@ng-select/ng-select": "^13.9.1", "@ngx-translate/core": "14.0.0", "@ngx-translate/http-loader": "7.0.0", "@popperjs/core": "^2.11.8", "@typescript-eslint/types": "^8.25.0", "@typescript-eslint/utils": "^8.25.0", "@yaireo/tagify": "^4.17.7", "angular-calendar": "^0.30.1", "angular-in-memory-web-api": "^0.18.0", "animate.css": "4.1.1", "apexcharts": "3.35.3", "apollo-angular": "^7.0.0", "aws-amplify": "^4.3.43", "aws-sdk": "^2.1336.0", "bootstrap": "^5.3.6", "bootstrap-icons": "^1.13.1", "date-fns": "^2.29.2", "graphql": "^15.8.0", "line-awesome": "1.3.0", "ng-apexcharts": "1.7.1", "ng-inline-svg-2": "^15.0.1", "ngx-chips": "^3.0.0", "ngx-image-compress": "^13.1.13", "ngx-mask": "^14.0.3", "ngx-spinner": "^16.0.2", "ngx-toastr": "^15.0.0", "nouislider": "15.6.0", "object-path": "0.11.8", "pdfmake": "^0.1.68", "prism-themes": "1.9.0", "prismjs": "1.28.0", "rxjs": "7.5.5", "socicon": "3.0.5", "tslib": "2.4.0", "typescript-eslint": "^8.32.1", "xlsx": "^0.18.5", "zone.js": "^0.14.10"}, "devDependencies": {"@angular-devkit/build-angular": "18.2.14", "@angular-eslint/builder": "18.4.3", "@angular-eslint/eslint-plugin": "18.4.3", "@angular-eslint/eslint-plugin-template": "18.4.3", "@angular-eslint/schematics": "18.4.3", "@angular-eslint/template-parser": "18.4.3", "@angular/cli": "18.2.14", "@angular/compiler-cli": "18.2.13", "@types/bootstrap": "^5.2.10", "@types/ckeditor__ckeditor5-core": "^37.0.4", "@types/ckeditor__ckeditor5-watchdog": "^29.0.1", "@types/jasmine": "4.0.3", "@types/node": "17.0.36", "@types/object-path": "0.11.1", "@types/pdfmake": "^0.2.10", "@types/prismjs": "1.26.0", "@typescript-eslint/eslint-plugin": "8.32.1", "@typescript-eslint/parser": "8.32.1", "eslint": "^9.0.0", "jasmine-core": "4.1.1", "karma": "6.3.20", "karma-chrome-launcher": "3.1.1", "karma-coverage": "2.2.0", "karma-jasmine": "5.0.1", "karma-jasmine-html-reporter": "2.0.0", "rtlcss-webpack-plugin": "4.0.7", "typescript": "^5.5.4", "webpack": "^5.73.0", "webpack-cli": "4.9.2", "webpack-rtl-plugin": "2.0.0"}, "resolutions": {"autoprefixer": "10.4.6"}}